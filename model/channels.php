<?php

// DB Connection
require('config.php');
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
require('controller/functions.php');

// Current user info
$notifications_count = check_notifications($current_user);
$user_info = getuserinfo($current_user);

// Redirect if user is client
if($user_info['permissions'] == '3') {
    header('Location: /home/');
}

// Theme and body class
$bodyclass = $user_info['theme'] ?? 'vous';
$bodyclass .= ' channels';


// Form management
if(isset($_POST['form_action']) && $_POST['form_action'] == 'edit_channel') {
    $channel_id = $_POST['channel'];
    $edit_channel = edit_channel($channel_id);
    header('Location: /channels/');
} elseif(isset($_POST['form_action']) && $_POST['form_action'] == 'edit_channel_formats') {
    $channel_id = $_POST['channel'];
    $edit_channel_formats = edit_channel_formats($channel_id);
    header('Location: /channels/');
}

// Getting elements
$channels = getchannels();
$users = getusers();

// Breadcrumbs
$breadcrumbs = '<li>Channels</li>';

require('view/channels.php');