<?php

// DB Connection
require('config.php');
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
require('controller/functions.php');

// Current user info
$notifications_count = check_notifications($current_user);
$user_info = getuserinfo($current_user);

// Theme and body class
$bodyclass = $user_info['theme'] ?? 'vous';
$bodyclass .= ' add_campaign';

// Getting elements
$clients = getclients();
$users = getusers();
$channels = getchannels();
$campaign_id = $_GET['campaign'];
$campaign = getcampaign($campaign_id);
$channels_formats = get_all_channel_formats();

// Breadcrumbs
$breadcrumbs = '<li><a href="/campaigns/">Campaigns</a></li><li><a href="/campaign/?campaign='.$campaign['id'].'">'.stripslashes($campaign['name']).'</a></li><li>Request assets</li>';

// Form management
if(isset($_POST['form_action']) && $_POST['form_action'] == 'request_assets') {
    $asset = add_multiple_assets();
    header('Location: /campaign/?campaign='.$campaign_id);
} else {
    require('view/request_assets.php');
}


