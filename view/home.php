<?php
ob_start(); 
?>

<div class="page_controls">
    <h1>Overview</h1>
    <div class="controls">
        <a href="/add_campaign/" class="btn">
            Create a campaign
        </a>
    </div>
</div>
<section>
    <div class="home_grid">
        <div class="grid_element">
            <h2>Currently running campaigns</h2>
            <?php if(isset($running_campaigns)) {
                echo '<p class="figure">'.count($running_campaigns).'</p>';
                ?>
                <div class="table">
                    <div class="tr">
                        <div class="th">Client</div>
                        <div class="th">Campaign name</div>
                        <div class="th">Start date</div>
                        <div class="th">End date</div>
                    </div>
                    <?php foreach($running_campaigns as $campaign) { ?>
                    <a class="tr" href="/campaign/?campaign=<?= $campaign['id'] ?>">
                        <div class="td"><span class="client" style="background-color:<?= $clients[$campaign['client']]['color'] ?>;"><?= $clients[$campaign['client']]['name'] ?></span></div>
                        <div class="td">
                            <?= stripslashes($campaign['name']) ?>
                        </div>
                        <div class="td"><?= date('d/m/Y', strtotime($campaign['date_start'])) ?></div>
                        <div class="td"><?= date('d/m/Y', strtotime($campaign['date_end'])) ?></div>
                    </a>
                <?php } ?>
                </div>
            <?php } else {
                echo '<p class="figure">0</p>';
                echo '<p style="text-align:center;"><em>No runing campaign.</em></p>';
            } ?>
        </div>
        
        <div class="grid_element">
            <h2>Upcoming campaigns</h2>
            <?php if(isset($next_campaigns)) {
                echo '<p class="figure">'.count($next_campaigns).'</p>';
                ?>
                <div class="table">
                    <div class="tr">
                        <div class="th">Client</div>
                        <div class="th">Campaign name</div>
                        <div class="th">Start date</div>
                        <div class="th">End date</div>
                    </div>
                    <?php foreach($next_campaigns as $campaign) { ?>
                    <a class="tr" href="/campaign/?campaign=<?= $campaign['id'] ?>">
                        <div class="td"><span class="client" style="background-color:<?= $clients[$campaign['client']]['color'] ?>;"><?= $clients[$campaign['client']]['name'] ?></span></div>
                        <div class="td"><?= stripslashes($campaign['name']) ?></div>
                        <div class="td"><?= date('d/m/Y', strtotime($campaign['date_start'])) ?></div>
                        <div class="td"><?= date('d/m/Y', strtotime($campaign['date_end'])) ?></div>
                    </a>
                <?php } ?>
                </div>
            <?php } else {
                echo '<p class="figure">0</p>';
                echo '<p style="text-align:center;"><em>No upcoming campaign.</em></p>';
            } ?>
        </div>
        
        <div class="grid_element">
            <h2>Campaigns to setup</h2>
            <?php if(isset($unactive_campaigns)) {
                echo '<p class="figure">'.count($unactive_campaigns).'</p>';
                ?>
                <div class="table">
                    <div class="tr">
                        <div class="th">Client</div>
                        <div class="th">Campaign name</div>
                        <div class="th">Start date</div>
                        <div class="th">End date</div>
                    </div>
                    <?php foreach($unactive_campaigns as $campaign) { ?>
                    <a class="tr" href="/campaign/?campaign=<?= $campaign['id'] ?>">
                        <div class="td"><span class="client" style="background-color:<?= $clients[$campaign['client']]['color'] ?>;"><?= $clients[$campaign['client']]['name'] ?></span></div>
                        <div class="td"><?= stripslashes($campaign['name']) ?></div>
                        <div class="td"><?= date('d/m/Y', strtotime($campaign['date_start'])) ?></div>
                        <div class="td"><?= date('d/m/Y', strtotime($campaign['date_end'])) ?></div>
                    </a>
                <?php } ?>
                </div>
            <?php } else {
                echo '<p class="figure">0</p>';
                echo '<p style="text-align:center;"><em>All campaigns are setup.</em></p>';
            } ?>
        </div>

        <div class="grid_element">
            <h2>Recently completed campaigns</h2>
            <?php if(isset($previous_campaigns)) {
                echo '<p class="figure">'.count($previous_campaigns).'</p>';
                ?>
                <div class="table">
                    <div class="tr">
                        <div class="th">Client</div>
                        <div class="th">Campaign name</div>
                        <div class="th">Start date</div>
                        <div class="th">End date</div>
                    </div>
                    <?php foreach($previous_campaigns as $campaign) { ?>
                    <a class="tr" href="/campaign/?campaign=<?= $campaign['id'] ?>">
                        <div class="td"><span class="client" style="background-color:<?= $clients[$campaign['client']]['color'] ?>;"><?= $clients[$campaign['client']]['name'] ?></span></div>
                        <div class="td"><?= stripslashes($campaign['name']) ?></div>
                        <div class="td"><?= date('d/m/Y', strtotime($campaign['date_start'])) ?></div>
                        <div class="td"><?= date('d/m/Y', strtotime($campaign['date_end'])) ?></div>
                    </a>
                <?php } ?>
                </div>
            <?php } else {
                echo '<p class="figure">0</p>';
                echo '<p style="text-align:center;"><em>Nothing to show.</em></p>';
            } ?>
        </div>
    </div>
</section>

<?php
$content = ob_get_clean();
require('view/template.php');