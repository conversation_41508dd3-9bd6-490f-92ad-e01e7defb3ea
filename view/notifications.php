<?php
ob_start(); 
?>

<h1>Notifications</h1>

<section>
    <div class="notifications_table">
        <?php if(isset($notifications)) {
            foreach($notifications as $notification) { ?>
                <div class="notification <?php if($notification['read'] == 0) {echo 'unread';} else {echo 'read';} ?>">
                    <span class="date">Le <?= date('d/m/Y à H:i:s', strtotime($notification['date'])) ?>
                    <p><?= $notification['content'] ?></p>
                </div>
            <?php }
        } ?>
    </div>
</section>

<?php
$content = ob_get_clean();
require('view/template.php');