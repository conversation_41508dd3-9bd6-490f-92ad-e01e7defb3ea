<div class="table asset_table">
    <div class="tr">
        <div class="th">Asset name</div>
        <div class="th">Channel</div>
        <div class="th">Dimensions</div>
        <div class="th">Format</div>
        <div class="th">Term</div>
        <div class="th">Language</div>
        <div class="th">Target</div>
        <div class="th">Device</div>
        <div class="th">Segment</div>
        <div class="th">Upload</div>
        <div class="th">UTM</div>
    </div>
    <?php foreach($assets as $asset) { 
        if(isset($asset['url']) && $asset['url'] != '' && $asset['channel'] != '' && $asset['dimensions'] != '' && $asset['format'] != '' && $asset['device'] != '' && $asset['target'] != '' && $asset['segment'] != '') {
            $utm_ok = true;
        } else {
            $utm_ok = false;
        }
        ?>
        <div class="tr asset" data-channel="<?= $channels[$asset['channel']]['slug'] ?>" data-format="<?= $asset['format'] ?>" data-status="<?= $asset['status'] ?>" data-language="<?= $asset['language'] ?>" data-project="<?= $campaign['project_number'] ?>" data-id="<?= $asset['id'] ?>">
            <div class="td">
                <?php if($asset['name'] != '') {
                    echo stripslashes($asset['name']);
                } else {
                    echo '<em>Untitled asset</em>';
                    if($utm_ok) echo '<span class="generate_asset_name"></span>';
                } ?>
            </div>
            <div class="td"><span class="channel"><?= $channels[$asset['channel']]['name'] ?></span></div>
            <div class="td"><?= $asset['dimensions'] ?? '-' ?></div>
            <div class="td"><?= $asset['format'] ?? '-' ?></div>
            <div class="td"><?= $asset['term'] ?? '-' ?></div>
            <div class="td"><?= $asset['language'] ?? '-' ?></div>
            <div class="td"><?= $asset['target'] ?? '-' ?></div>
            <div class="td"><?= $asset['device'] ?? '-' ?></div>
            <div class="td"><?= $asset['segment'] ?? '-' ?></div>
            <div class="td"><?php if($asset['upload'] != '') echo '<i class="fa-regular fa-circle-check"></i>'; else echo '<i class="fa-regular fa-circle-xmark" style="color:red;"></i>'; ?></div>
            <div class="td">
                <div class="asset_utm">
                    <?php 
                    if($utm_ok) {
                        if(str_contains($asset['url'], '?')) {
                            if(str_contains($asset['url'], '#')) {
                                $url_split = explode('#', $asset['url']);
                                $url_normal = $url_split[0];
                                $url_anchor = $url_split[1];
                                $utm = $url_normal.'&utm_source='.$channels[$asset['channel']]['slug'].'&utm_medium='.$asset['dimensions'].'&utm_campaign='.$campaign['campaign_slug'].'&utm_content='.$asset['format'].'&utm_term='.$asset['term'].'&utm_id=id_'.$campaign['project_number'].'_'.$asset['id'].'#'.$url_anchor;
                            } else {
                                $utm = $asset['url'].'&utm_source='.$channels[$asset['channel']]['slug'].'&utm_medium='.$asset['dimensions'].'&utm_campaign='.$campaign['campaign_slug'].'&utm_content='.$asset['format'].'&utm_term='.$asset['term'].'&utm_id=id_'.$campaign['project_number'].'_'.$asset['id'];
                            }
                        } else {
                            if(str_contains($asset['url'], '#')) {
                                $url_split = explode('#', $asset['url']);
                                $url_normal = $url_split[0];
                                $url_anchor = $url_split[1];
                                $utm = $url_normal.'?utm_source='.$channels[$asset['channel']]['slug'].'&utm_medium='.$asset['dimensions'].'&utm_campaign='.$campaign['campaign_slug'].'&utm_content='.$asset['format'].'&utm_term='.$asset['term'].'&utm_id=id_'.$campaign['project_number'].'_'.$asset['id'].'#'.$url_anchor;
                            } else {
                                $utm = $asset['url'].'?utm_source='.$channels[$asset['channel']]['slug'].'&utm_medium='.$asset['dimensions'].'&utm_campaign='.$campaign['campaign_slug'].'&utm_content='.$asset['format'].'&utm_term='.$asset['term'].'&utm_id=id_'.$campaign['project_number'].'_'.$asset['id'];
                            }
                        }
                        echo '<input type="hidden" class="input-utm" value="'.$utm.'" />';
                        echo '<span class="copy copy_utm"><strong>Copied</strong></span>';
                    } else {
                        echo '<i class="fa-regular fa-circle-xmark" style="color:red;" title="UTM can\'t be generated."></i>';
                    } ?>
                </div>
            </div>
        </div>

    <?php } ?>
</div>