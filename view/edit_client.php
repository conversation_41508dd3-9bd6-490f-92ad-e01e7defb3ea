<?php
ob_start(); 
?>

<div class="page_controls">
    <h1>Edit a client</h1>
</div>


<div class="form_wrapper add_client">
    <form action="/clients/" method="post">
        <section>
            <div class="form-group">
                <label for="name">Client name</label>
                <input type="text" name="name" id="name" placeholder="Enter the client's name" value="<?= stripslashes($client['name']) ?? '' ?>" required />
            </div>
            <div class="form-group">
                <label for="slug">Client slug</label>
                <input type="text" name="slug" id="slug" placeholder="Enter the client's slug" value="<?= stripslashes($client['slug']) ?? '' ?>" required />
            </div>
            <div class="form-group">
                <label for="color">Client color</label>
                <input type="color" name="color" id="color" value="<?= stripslashes($client['color']) ?? '' ?>" />
            </div>
            <div class="form-group">
                <label for="industry">Client industry</label>
                <select name="industry">
                    <option selected disabled>No industry</option>
                    <?php 
                    $industries = ["Automotive","Food & Beverage","Insurance","Banking & Finance","Construction & Real Estate","Retail & Commerce","Education & Training","Energy & Environment","Hospitality & Tourism","IT & Software","Internet & E-commerce","Legal","Logistics & Transportation","Luxury & Fashion","Media & Communication","Pharmaceutical & Healthcare","Advertising & Marketing","Public Sector & Administration","Business Services","Sports & Entertainment","Startups & Innovation","Telecommunications","Staffing & Recruitment","Manufacturing & Industry","Non-profit & NGOs","Beauty & Wellness"]; 
                    asort($industries);
                    foreach($industries as $industry) {
                        echo '<option value="'.$industry.'"';
                        if($industry == $client['industry']) echo ' selected';
                        echo '>'.$industry.'</option>';
                    }
                    ?>
                    <option value="Other">Other</option>
                </select>
            </div>
            <div class="form-group">
                <label for="googleads_id">Google Ads Customer ID</label>
                <input type="text" name="googleads_id" id="googleads_id" placeholder="Enter Google Ads Customer ID" value="<?= $client['googleads_id'] ?>" />
            </div>
            <div class="form-group">
                <label for="googleads_id">Pinterest Ads Customer ID</label>
                <input type="text" name="pinterest_id" id="pinterest_id" placeholder="Enter Pinterest Ads Customer ID" value="<?= $client['pinterest_id'] ?>" />
            </div>
        </section>
        <section>
            <h3>Client commission rates</h3>
            <?php if(isset($channels)) {
                foreach($channels as $channel) { ?>
                    <div class="form-group form-group-inline">
                        <label for="<?= $channel['slug'] ?>"><?= $channel['name'] ?></label>
                        <input type="hidden" name="commission_channel_id[]" value="<?= $channel['id'] ?>" />
                        <input type="number" min="0" step="0.01" name="commission_channel_rate[]" id="<?= $channel['slug'] ?>" placeholder="Commission rate for <?= $channel['name'] ?>" value="<?= $commissions[$channel['id']]['commission'] ?>" />
                    </div>
                <?php }
            } ?>
        </section>
        <div class="form-submit">
            <input type="hidden" name="form_action" value="edit_client" />
            <input type="hidden" name="client" value="<?= $client['id'] ?>" />
            <input type="submit" value="Edit client" class="btn" />
        </div>
    </form>
</div>

<?php
$content = ob_get_clean();
require('view/template.php');