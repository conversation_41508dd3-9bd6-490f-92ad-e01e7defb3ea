<?php
ob_start(); 
?>

<h1>Logs</h1>

<section>
    <div class="notifications_table">
        <?php if(isset($logs)) {
            foreach($logs as $log) { ?>
                <div class="notification">
                    <span class="date">Le <?= date('d/m/Y à H:i:s', strtotime($log['date'])) ?>
                    <p>
                        <?php 
                        echo $users[$log['user']]['first_name'].' '.$users[$log['user']]['last_name'].' '; 
                        if($log['action'] == 'edit_user') {
                            echo 'a modifié le profil de ';
                            echo '<a href="/edit_user/?user='.$log['element'].'">'.$users[$log['element']]['first_name'].' '.$users[$log['element']]['last_name'].'</a>.';
                        }
                        if($log['action'] == 'add_user') {
                            echo 'a ajouté ';
                            echo '<a href="/edit_user/?user='.$log['element'].'">'.$users[$log['element']]['first_name'].' '.$users[$log['element']]['last_name'].'</a> comme nouvel utilisateur.';
                        }
                        if($log['action'] == 'edit_info') {
                            echo 'a modifié ses informations personnelles.';
                        }
                        if($log['action'] == 'add_asset') {
                            echo 'a ajouté un asset à la campagne ';
                            echo '<a href="/campaign/?campaign='.$log['element'].'">'.$campaigns[$log['element']]['name'].'</a>.';
                        }
                        if($log['action'] == 'edit_asset') {
                            echo 'a modifié un asset ';
                            echo '<a href="/campaign/?campaign='.$log['element'].'">d\'une campagne</a>.';
                        }
                        if($log['action'] == 'campaign_validate') {
                            echo 'a validé la campagne ';
                            echo '<a href="/campaign/?campaign='.$log['element'].'">'.($campaigns[$log['element']]['name'] ?? '<em>Deleted</em>').'</a>.';
                        }
                        if($log['action'] == 'campaign_unvalidate') {
                            echo 'a retiré la campagne ';
                            echo '<a href="/campaign/?campaign='.$log['element'].'">'.($campaigns[$log['element']]['name'] ?? '<em>Deleted</em>').'</a> du planning.';
                        }
                        if($log['action'] == 'edit_campaign') {
                            echo 'a modifié les informations de la campagne ';
                            echo '<a href="/campaign/?campaign='.$log['element'].'">'.($campaigns[$log['element']]['name'] ?? '<em>Deleted</em>').'</a>.';
                        }
                        if($log['action'] == 'add_channel') {
                            echo 'a ajouté le canal ';
                            echo '<a href="/edit_channel/?channel='.$log['element'].'">'.($channels[$log['element']]['name'] ?? '<em>Deleted</em>').'</a>.';
                        }
                        if($log['action'] == 'edit_channel') {
                            echo 'a modifié le canal ';
                            echo '<a href="/edit_channel/?channel='.$log['element'].'">'.($channels[$log['element']]['name'] ?? '<em>Deleted</em>').'</a>.';
                        }
                        if($log['action'] == 'add_client') {
                            echo 'a ajouté ';
                            echo '<a href="/edit_client/?client='.$log['element'].'">un nouveau client</a>.';
                        }
                        if($log['action'] == 'edit_client') {
                            echo 'a modifié les informations ';
                            echo '<a href="/edit_client/?client='.$log['element'].'">d\'un client</a>.';
                        }
                        if($log['action'] == 'add_campaign') {
                            echo 'a ajouté une nouvelle campagne : ';
                            echo '<a href="/campaign/?campaign='.$log['element'].'">'.($campaigns[$log['element']]['name'] ?? '<em>Deleted</em>' ).'</a>.';
                        }
                        if($log['action'] == 'delete_campaign') {
                            echo 'a supprimé la campagne #'.$log['element'].'.';
                        }
                        if($log['action'] == 'delete_asset') {
                            echo 'a supprimé un asset.';
                        }
                        if($log['action'] == 'add_reporting') {
                            echo 'a ajouté un reporting à la campagne ';
                            echo '<a href="/campaign/?campaign='.$log['element'].'">'.($campaigns[$log['element']]['name'] ?? '<em>Deleted</em>').'</a>.';
                        }
                        if($log['action'] == 'delete_reporting') {
                            echo 'a supprimé un reporting à la campagne ';
                            echo '<a href="/campaign/?campaign='.$log['element'].'">'.($campaigns[$log['element']]['name'] ?? '<em>Deleted</em>').'</a>.';
                        }
                        if($log['action'] == 'edit_channel_formats') {
                            echo 'a modifié le catalogue du canal ';
                            echo '<a href="/edit_channel/?channel='.$log['element'].'">'.($channels[$log['element']]['name'] ?? '<em>Deleted</em>').'</a>.';
                        }
                        if($log['action'] == 'login') {
                            echo 's\'est connecté au campaign manager.';
                        }
                        ?>
                    </p>
                </div>
            <?php }
        } ?>
    </div>
</section>

<?php
$content = ob_get_clean();
require('view/template.php');